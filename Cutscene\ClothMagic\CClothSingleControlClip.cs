using System.Collections.Generic;
using CEditor;
using Sirenix.OdinInspector;
using UnityEngine;
using MagicaCloth2;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CClothClipSingleParams
    {
        [SerializeField, LabelText("角色名")]
        public string characterName = "";

        [SerializeField, LabelText("进入时启用全部布料")]
        public bool enterEnableCloth = true;
        
        [SerializeField, LabelText("进入时全部布料权重"), Tooltip("小于0时不覆盖设置"), Range(-1.0f, 1.0f)]
        public float enterBlendWeight = -1.0f;
        
        [SerializeField, LabelText("进入时单个布料配置")]
        public List<ClothClipSerializeData> enterClothConfigs = new();
    }
    [Name("布料配置(单点)")]
    [Attachable(typeof(CClothControlTrack))]
    [Category("C类编辑器")]
    public class CClothSingleControlClip : BaseDialogueActorActionClip<CClothClipSingleParams>
    {
        private GameObject performer;
        private CEditor.ClothManager clothMaster;
        private bool _shouldChange = false;

        [SerializeField, HideInInspector]
        private float _length = 1 / 30.0f;
        private CClothClipSingleParams _tmpEnterRestoredParams = new();

        public override float length
        {
            get { return _length; }
            // set { _length = value; }
        }

        public override string info
        {
            get { return "布料配置(单点)"; }
        }

        protected override void OnCreate()
        {
            base.OnCreate();

            var actor = GetActor();
            if (actor != null)
            {
                GetCfg().characterName = actor.name;
            }
        }

        //Called in forward sampling when the clip is entered
        protected override void OnEnter()
        {
            _tmpEnterRestoredParams = CollectCurrentClothParams();
            ExeChange(GetCfg().characterName, GetCfg().enterClothConfigs, GetCfg().enterEnableCloth, GetCfg().enterBlendWeight ,false);
        }

        protected override void OnReverse()
        {
            ExeChange(GetCfg().characterName, _tmpEnterRestoredParams.enterClothConfigs,  _tmpEnterRestoredParams.enterEnableCloth, _tmpEnterRestoredParams.enterBlendWeight, true);
        }

        private void ExeChange(string charName, List<ClothClipSerializeData> inClothData, bool enable, float blendWeight, bool restore)
        {
            if (performer == null)
                performer = GetActor();
            if (clothMaster == null)
                clothMaster = performer.GetComponent<CEditor.ClothManager>();
            if (clothMaster == null)
            {
                clothMaster = performer.AddComponent<CEditor.ClothManager>();
                clothMaster.Init();
            }

            if (clothMaster != null)
                clothMaster.SetClothManagerState(charName, inClothData, enable, blendWeight, restore);
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {
            if (clothMaster != null)
            {
                clothMaster.SetTimelineTrackControl(true);
            }
        }

        private CClothClipSingleParams CollectCurrentClothParams()
        {
            var actor = GetActor();
            if (actor == null)
            {
                return new CClothClipSingleParams()
                {
                    characterName = "",
                    enterEnableCloth = false,
                    enterBlendWeight = -1.0f,
                    enterClothConfigs = new List<ClothClipSerializeData>()
                };
            }

            var clothManager = actor.GetComponent<CEditor.ClothManager>();
            if (clothManager == null)
            {
                return new CClothClipSingleParams()
                {
                    characterName = actor.name,
                    enterEnableCloth = false,
                    enterBlendWeight = -1.0f,
                    enterClothConfigs = new List<ClothClipSerializeData>()
                };
            }

            var currentConfigs = clothManager.GetCurrentClothConfigs();
            
            return new CClothClipSingleParams()
            {
                characterName = actor.name,
                enterEnableCloth = clothManager.IsEnable(),
                enterBlendWeight = clothManager.GetBlendWeight(),
                enterClothConfigs = currentConfigs
            };
        }
    }
}